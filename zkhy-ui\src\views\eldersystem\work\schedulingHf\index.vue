<template>
  <div class="scheduling-hf-wrapper">
    <!-- 筛选条件区域 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <el-form :model="filterForm" label-width="100px" :inline="true">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="排班日期">
                <div class="date-range-wrapper">
                  <el-button-group class="period-nav">
                    <el-button @click="handlePrevPeriod" :icon="ArrowLeft" size="small">上一周期</el-button>
                    <el-button @click="handleNextPeriod" :icon="ArrowRight" size="small">下一周期</el-button>
                  </el-button-group>
                  <el-date-picker
                    v-model="filterForm.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    @change="handleDateRangeChange"
                    :clearable="false"
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="区域">
                <el-select
                  v-model="filterForm.areas"
                  placeholder="请选择区域"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  clearable
                  @change="handleFilterChange"
                >
                  <el-option
                    v-for="area in areaOptions"
                    :key="area.value"
                    :label="area.label"
                    :value="area.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="班次">
                <el-select
                  v-model="filterForm.shifts"
                  placeholder="请选择班次"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  clearable
                  @change="handleFilterChange"
                >
                  <el-option
                    v-for="shift in shiftOptions"
                    :key="shift.value"
                    :label="shift.label"
                    :value="shift.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="人员">
                <el-input
                  v-model="filterForm.staffName"
                  placeholder="姓名/工号模糊查询"
                  clearable
                  @input="handleFilterChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="状态">
                <el-select
                  v-model="filterForm.status"
                  placeholder="请选择状态"
                  multiple
                  collapse-tags
                  clearable
                  @change="handleFilterChange"
                >
                  <el-option
                    v-for="status in statusOptions"
                    :key="status.value"
                    :label="status.label"
                    :value="status.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-form-item>
                <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar-section">
      <el-card class="toolbar-card">
        <div class="toolbar-content">
          <div class="toolbar-left">
            <el-button type="primary" @click="openAddStaffDialog" :icon="Plus">增加人员</el-button>
            <el-button type="success" @click="openImportDialog" :icon="Upload">导入</el-button>
            <el-button type="info" @click="openWorkHoursDialog" :icon="DataAnalysis">工时统计</el-button>
          </div>
          <div class="toolbar-right">
            <span class="period-info">{{ currentPeriodInfo }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 排班表格 -->
    <div class="schedule-table-section">
      <el-card class="table-card">
        <div class="table-header">
          <h3>{{ currentYear }}年{{ currentMonth }}月排班表</h3>
        </div>
        <div class="table-wrapper" ref="tableWrapper">
          <el-table
            :data="scheduleTableData"
            border
            stripe
            height="600"
            :cell-class-name="getCellClassName"
            @cell-click="handleCellClick"
            @cell-contextmenu="handleCellRightClick"
            ref="scheduleTable"
          >
            <!-- 人员列 -->
            <el-table-column
              prop="staffName"
              label="人员"
              width="120"
              fixed="left"
              align="center"
            >
              <template #default="scope">
                <div class="staff-info">
                  <div class="staff-name">{{ scope.row.staffName }}</div>
                  <div class="staff-code">{{ scope.row.staffCode }}</div>
                </div>
              </template>
            </el-table-column>

            <!-- 日期列 -->
            <el-table-column
              v-for="(date, index) in dateColumns"
              :key="date.date"
              :label="date.label"
              :prop="`day${index + 1}`"
              width="100"
              align="center"
              :class-name="date.isWeekend ? 'weekend-column' : ''"
            >
              <template #header>
                <div class="date-header">
                  <div class="date-day">{{ date.day }}</div>
                  <div class="date-week">{{ date.week }}</div>
                </div>
              </template>
              <template #default="scope">
                <div
                  class="schedule-cell"
                  :class="{
                    'has-schedule': scope.row[`day${index + 1}`],
                    'conflict': hasConflict(scope.row.staffId, date.date),
                    'selected': isSelected(scope.row.staffId, date.date)
                  }"
                  @click.stop="handleCellClick(scope.row, scope.column, scope.cell, $event)"
                  @contextmenu.prevent="handleCellRightClick(scope.row, scope.column, scope.cell, $event)"
                >
                  <div v-if="scope.row[`day${index + 1}`]" class="schedule-content">
                    <div class="shift-info">
                      <span class="shift-name">{{ scope.row[`day${index + 1}`].shiftName }}</span>
                      <span class="area-name">{{ scope.row[`day${index + 1}`].areaName || '-' }}</span>
                    </div>
                  </div>
                  <div v-if="hasConflict(scope.row.staffId, date.date)" class="conflict-indicator">
                    <el-icon class="conflict-icon"><WarningFilled /></el-icon>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 单元格设置弹窗 -->
    <el-dialog
      v-model="cellDialogVisible"
      title="排班设置"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="cellForm" label-width="80px">
        <el-form-item label="人员">
          <el-input v-model="cellForm.staffName" readonly />
        </el-form-item>
        <el-form-item label="日期">
          <el-input v-model="cellForm.date" readonly />
        </el-form-item>
        <el-form-item label="班次" required>
          <el-select v-model="cellForm.shiftId" placeholder="请选择班次" clearable>
            <el-option
              v-for="shift in shiftOptions"
              :key="shift.value"
              :label="shift.label"
              :value="shift.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-select v-model="cellForm.areaId" placeholder="请选择区域" clearable>
            <el-option
              v-for="area in areaOptions"
              :key="area.value"
              :label="area.label"
              :value="area.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cellDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCellSave">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量设置弹窗 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量排班设置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchForm" label-width="80px">
        <el-form-item label="选中单元格">
          <div class="selected-cells-info">
            <span>已选择 {{ selectedCells.length }} 个单元格</span>
          </div>
        </el-form-item>
        <el-form-item label="班次" required>
          <el-select v-model="batchForm.shiftId" placeholder="请选择班次" clearable>
            <el-option
              v-for="shift in shiftOptions"
              :key="shift.value"
              :label="shift.label"
              :value="shift.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-select v-model="batchForm.areaId" placeholder="请选择区域" clearable>
            <el-option
              v-for="area in areaOptions"
              :key="area.value"
              :label="area.label"
              :value="area.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchSave">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 增加人员弹窗 -->
    <el-dialog
      v-model="addStaffDialogVisible"
      title="增加人员"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="add-staff-content">
        <div class="staff-search">
          <el-input
            v-model="staffSearchKeyword"
            placeholder="请输入姓名或工号搜索"
            clearable
            @input="handleStaffSearch"
          />
        </div>
        <div class="staff-table">
          <el-table
            :data="availableStaffList"
            @selection-change="handleStaffSelectionChange"
            height="400"
            border
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="staffName" label="姓名" width="120" />
            <el-table-column prop="staffCode" label="工号" width="120" />
            <el-table-column prop="department" label="科室" width="120" />
            <el-table-column prop="position" label="身份" width="100" />
            <el-table-column prop="phone" label="联系电话" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addStaffDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddStaff">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工时统计弹窗 -->
    <el-dialog
      v-model="workHoursDialogVisible"
      title="工时统计"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="work-hours-content">
        <div class="work-hours-filter">
          <el-form :model="workHoursForm" :inline="true">
            <el-form-item label="统计日期">
              <el-date-picker
                v-model="workHoursForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleWorkHoursQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleWorkHoursQuery">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="work-hours-chart" ref="workHoursChart" style="height: 400px;"></div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="workHoursDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入排班数据"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="import-content">
        <div class="import-tips">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 请下载模板文件，按照模板格式填写排班数据</p>
              <p>2. 支持 .xlsx 和 .xls 格式文件</p>
              <p>3. 导入前请确保数据格式正确</p>
            </template>
          </el-alert>
        </div>
        <div class="import-actions">
          <el-button type="primary" @click="downloadTemplate">下载模板</el-button>
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept=".xlsx,.xls"
          >
            <el-button type="success">选择文件</el-button>
          </el-upload>
        </div>
        <div v-if="uploadFile" class="upload-file-info">
          <p>已选择文件：{{ uploadFile.name }}</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleImport" :disabled="!uploadFile">导入</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
      ref="contextMenu"
    >
      <div class="menu-item" @click="handleContextMenuAction('set')">设置排班</div>
      <div class="menu-item" @click="handleContextMenuAction('clear')">清除排班</div>
      <div class="menu-item" @click="handleContextMenuAction('copy')">复制</div>
      <div class="menu-item" @click="handleContextMenuAction('paste')">粘贴</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Plus,
  Upload,
  DataAnalysis,
  ArrowLeft,
  ArrowRight,
  WarningFilled
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const filterForm = ref({
  dateRange: [],
  areas: [],
  shifts: [],
  staffName: '',
  status: []
})

const scheduleTableData = ref([])
const dateColumns = ref([])
const selectedCells = ref([])
const contextMenuVisible = ref(false)
const contextMenuPosition = ref({ x: 0, y: 0 })

// 弹窗控制
const cellDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const addStaffDialogVisible = ref(false)
const workHoursDialogVisible = ref(false)
const importDialogVisible = ref(false)

// 表单数据
const cellForm = ref({
  staffId: '',
  staffName: '',
  date: '',
  shiftId: '',
  areaId: ''
})

const batchForm = ref({
  shiftId: '',
  areaId: ''
})

const workHoursForm = ref({
  dateRange: []
})

// 其他数据
const availableStaffList = ref([])
const staffSearchKeyword = ref('')
const selectedStaff = ref([])
const uploadFile = ref(null)
const currentCellInfo = ref(null)
const copiedSchedule = ref(null)

// 选项数据
const areaOptions = ref([
  { value: 'area1', label: '一区' },
  { value: 'area2', label: '二区' },
  { value: 'area3', label: '三区' },
  { value: 'area4', label: '四区' }
])

const shiftOptions = ref([
  { value: 'morning', label: '早班', color: '#67c23a' },
  { value: 'afternoon', label: '中班', color: '#e6a23c' },
  { value: 'evening', label: '晚班', color: '#f56c6c' },
  { value: 'night', label: '夜班', color: '#909399' },
  { value: 'rest', label: '休班', color: '#409eff' },
  { value: 'leave', label: '请假', color: '#c0c4cc' },
  { value: 'compensatory', label: '调休', color: '#95d475' }
])

const statusOptions = ref([
  { value: 'unscheduled', label: '未排班' },
  { value: 'scheduled', label: '已排班' }
])

// 计算属性
const currentYear = computed(() => {
  if (filterForm.value.dateRange && filterForm.value.dateRange.length > 0) {
    return new Date(filterForm.value.dateRange[0]).getFullYear()
  }
  return new Date().getFullYear()
})

const currentMonth = computed(() => {
  if (filterForm.value.dateRange && filterForm.value.dateRange.length > 0) {
    return new Date(filterForm.value.dateRange[0]).getMonth() + 1
  }
  return new Date().getMonth() + 1
})

const currentPeriodInfo = computed(() => {
  if (filterForm.value.dateRange && filterForm.value.dateRange.length === 2) {
    return `${filterForm.value.dateRange[0]} 至 ${filterForm.value.dateRange[1]}`
  }
  return ''
})

// 初始化数据
const initializeData = () => {
  // 设置默认日期范围（当前月的1号到月底）
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  filterForm.value.dateRange = [
    formatDate(firstDay),
    formatDate(lastDay)
  ]

  generateDateColumns()
  generateMockData()
}

// 生成日期列
const generateDateColumns = () => {
  if (!filterForm.value.dateRange || filterForm.value.dateRange.length !== 2) return

  const startDate = new Date(filterForm.value.dateRange[0])
  const endDate = new Date(filterForm.value.dateRange[1])
  const columns = []

  const current = new Date(startDate)
  while (current <= endDate) {
    const weekDays = ['日', '一', '二', '三', '四', '五', '六']
    columns.push({
      date: formatDate(current),
      day: current.getDate(),
      week: weekDays[current.getDay()],
      isWeekend: current.getDay() === 0 || current.getDay() === 6,
      label: `${current.getDate()}日`
    })
    current.setDate(current.getDate() + 1)
  }

  dateColumns.value = columns
}

// 生成模拟数据
const generateMockData = () => {
  const mockStaff = [
    { staffId: '001', staffName: '张护士', staffCode: 'N001', department: '内科', position: '护士' },
    { staffId: '002', staffName: '李护士', staffCode: 'N002', department: '外科', position: '护士' },
    { staffId: '003', staffName: '王护理员', staffCode: 'N003', department: '内科', position: '护理员' },
    { staffId: '004', staffName: '赵护士', staffCode: 'N004', department: '急诊科', position: '护士' },
    { staffId: '005', staffName: '陈护理员', staffCode: 'N005', department: '外科', position: '护理员' }
  ]

  const tableData = mockStaff.map(staff => {
    const rowData = { ...staff }

    // 为每一天生成随机排班数据
    dateColumns.value.forEach((date, index) => {
      const dayKey = `day${index + 1}`
      // 随机决定是否有排班（70%概率有排班）
      if (Math.random() > 0.3) {
        const randomShift = shiftOptions.value[Math.floor(Math.random() * shiftOptions.value.length)]
        const randomArea = areaOptions.value[Math.floor(Math.random() * areaOptions.value.length)]

        rowData[dayKey] = {
          shiftId: randomShift.value,
          shiftName: randomShift.label,
          areaId: randomArea.value,
          areaName: randomArea.label,
          date: date.date
        }
      }
    })

    return rowData
  })

  scheduleTableData.value = tableData
}

// 工具函数
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 事件处理函数
const handlePrevPeriod = () => {
  if (filterForm.value.dateRange && filterForm.value.dateRange.length === 2) {
    const startDate = new Date(filterForm.value.dateRange[0])
    const endDate = new Date(filterForm.value.dateRange[1])

    // 计算上一个周期的天数
    const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1

    const newStartDate = new Date(startDate)
    newStartDate.setDate(newStartDate.getDate() - daysDiff)

    const newEndDate = new Date(endDate)
    newEndDate.setDate(newEndDate.getDate() - daysDiff)

    filterForm.value.dateRange = [
      formatDate(newStartDate),
      formatDate(newEndDate)
    ]

    generateDateColumns()
    generateMockData()
  }
}

const handleNextPeriod = () => {
  if (filterForm.value.dateRange && filterForm.value.dateRange.length === 2) {
    const startDate = new Date(filterForm.value.dateRange[0])
    const endDate = new Date(filterForm.value.dateRange[1])

    // 计算当前周期的天数
    const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1

    const newStartDate = new Date(startDate)
    newStartDate.setDate(newStartDate.getDate() + daysDiff)

    const newEndDate = new Date(endDate)
    newEndDate.setDate(newEndDate.getDate() + daysDiff)

    filterForm.value.dateRange = [
      formatDate(newStartDate),
      formatDate(newEndDate)
    ]

    generateDateColumns()
    generateMockData()
  }
}

const handleDateRangeChange = () => {
  generateDateColumns()
  generateMockData()
}

const handleFilterChange = () => {
  // 根据筛选条件过滤数据
  console.log('筛选条件变化:', filterForm.value)
}

const handleSearch = () => {
  ElMessage.success('查询成功')
  generateMockData()
}

// 表格相关方法
const getCellClassName = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) return ''

  const dateIndex = columnIndex - 1
  if (dateIndex < dateColumns.value.length) {
    const date = dateColumns.value[dateIndex]
    if (date.isWeekend) {
      return 'weekend-cell'
    }
  }
  return ''
}

const handleCellClick = (row, column, cell, event) => {
  if (column.property === 'staffName') return

  // 获取日期索引
  const dayMatch = column.property.match(/day(\d+)/)
  if (!dayMatch) return

  const dayIndex = parseInt(dayMatch[1]) - 1
  if (dayIndex >= dateColumns.value.length) return

  const date = dateColumns.value[dayIndex]

  // 检查是否按住Ctrl键进行多选
  if (event.ctrlKey) {
    const cellKey = `${row.staffId}-${date.date}`
    const existingIndex = selectedCells.value.findIndex(cell => cell.key === cellKey)

    if (existingIndex > -1) {
      selectedCells.value.splice(existingIndex, 1)
    } else {
      selectedCells.value.push({
        key: cellKey,
        staffId: row.staffId,
        staffName: row.staffName,
        date: date.date,
        dayIndex
      })
    }
  } else {
    // 单个单元格设置
    selectedCells.value = []

    cellForm.value = {
      staffId: row.staffId,
      staffName: row.staffName,
      date: date.date,
      shiftId: row[column.property]?.shiftId || '',
      areaId: row[column.property]?.areaId || ''
    }

    currentCellInfo.value = {
      row,
      column,
      dayIndex
    }

    cellDialogVisible.value = true
  }
}

const handleCellRightClick = (row, column, cell, event) => {
  event.preventDefault()

  if (column.property === 'staffName') return

  // 获取日期索引
  const dayMatch = column.property.match(/day(\d+)/)
  if (!dayMatch) return

  const dayIndex = parseInt(dayMatch[1]) - 1
  if (dayIndex >= dateColumns.value.length) return

  const date = dateColumns.value[dayIndex]
  const cellKey = `${row.staffId}-${date.date}`

  // 如果当前单元格不在选中列表中，则清空选中列表并添加当前单元格
  if (!selectedCells.value.some(cell => cell.key === cellKey)) {
    selectedCells.value = [{
      key: cellKey,
      staffId: row.staffId,
      staffName: row.staffName,
      date: date.date,
      dayIndex
    }]
  }

  // 显示右键菜单
  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  }
  contextMenuVisible.value = true

  currentCellInfo.value = {
    row,
    column,
    dayIndex
  }
}

// 冲突检测
const hasConflict = (staffId, date) => {
  // 模拟冲突检测逻辑
  // 这里可以根据实际业务规则进行冲突检测
  return false
}

// 选中状态检测
const isSelected = (staffId, date) => {
  const cellKey = `${staffId}-${date}`
  return selectedCells.value.some(cell => cell.key === cellKey)
}

// 单元格保存
const handleCellSave = () => {
  if (!cellForm.value.shiftId) {
    ElMessage.warning('请选择班次')
    return
  }

  const { row, column, dayIndex } = currentCellInfo.value
  const shift = shiftOptions.value.find(s => s.value === cellForm.value.shiftId)
  const area = areaOptions.value.find(a => a.value === cellForm.value.areaId)

  // 更新表格数据
  const targetRow = scheduleTableData.value.find(r => r.staffId === cellForm.value.staffId)
  if (targetRow) {
    targetRow[column.property] = {
      shiftId: cellForm.value.shiftId,
      shiftName: shift?.label || '',
      areaId: cellForm.value.areaId,
      areaName: area?.label || '',
      date: cellForm.value.date
    }
  }

  cellDialogVisible.value = false
  ElMessage.success('排班设置成功')
}

// 批量保存
const handleBatchSave = () => {
  if (!batchForm.value.shiftId) {
    ElMessage.warning('请选择班次')
    return
  }

  const shift = shiftOptions.value.find(s => s.value === batchForm.value.shiftId)
  const area = areaOptions.value.find(a => a.value === batchForm.value.areaId)

  selectedCells.value.forEach(cell => {
    const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
    if (targetRow) {
      const dayKey = `day${cell.dayIndex + 1}`
      targetRow[dayKey] = {
        shiftId: batchForm.value.shiftId,
        shiftName: shift?.label || '',
        areaId: batchForm.value.areaId,
        areaName: area?.label || '',
        date: cell.date
      }
    }
  })

  selectedCells.value = []
  batchDialogVisible.value = false
  ElMessage.success('批量排班设置成功')
}

// 右键菜单操作
const handleContextMenuAction = (action) => {
  contextMenuVisible.value = false

  switch (action) {
    case 'set':
      if (selectedCells.value.length === 1) {
        // 单个设置
        const cell = selectedCells.value[0]
        const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
        const dayKey = `day${cell.dayIndex + 1}`

        cellForm.value = {
          staffId: cell.staffId,
          staffName: cell.staffName,
          date: cell.date,
          shiftId: targetRow?.[dayKey]?.shiftId || '',
          areaId: targetRow?.[dayKey]?.areaId || ''
        }

        cellDialogVisible.value = true
      } else if (selectedCells.value.length > 1) {
        // 批量设置
        batchForm.value = {
          shiftId: '',
          areaId: ''
        }
        batchDialogVisible.value = true
      }
      break
    case 'clear':
      selectedCells.value.forEach(cell => {
        const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
        if (targetRow) {
          const dayKey = `day${cell.dayIndex + 1}`
          delete targetRow[dayKey]
        }
      })
      selectedCells.value = []
      ElMessage.success('清除成功')
      break
    case 'copy':
      if (selectedCells.value.length === 1) {
        const cell = selectedCells.value[0]
        const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
        const dayKey = `day${cell.dayIndex + 1}`
        copiedSchedule.value = targetRow?.[dayKey] || null
        ElMessage.success('复制成功')
      }
      break
    case 'paste':
      if (copiedSchedule.value) {
        selectedCells.value.forEach(cell => {
          const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
          if (targetRow) {
            const dayKey = `day${cell.dayIndex + 1}`
            targetRow[dayKey] = { ...copiedSchedule.value, date: cell.date }
          }
        })
        selectedCells.value = []
        ElMessage.success('粘贴成功')
      } else {
        ElMessage.warning('没有可粘贴的内容')
      }
      break
  }
}

// 增加人员相关
const openAddStaffDialog = () => {
  // 生成可选人员列表（排除已在排班表中的人员）
  const existingStaffIds = scheduleTableData.value.map(row => row.staffId)

  const allStaff = [
    { staffId: '006', staffName: '孙护士', staffCode: 'N006', department: '儿科', position: '护士', phone: '13800000006' },
    { staffId: '007', staffName: '周护理员', staffCode: 'N007', department: '妇科', position: '护理员', phone: '13800000007' },
    { staffId: '008', staffName: '吴护士', staffCode: 'N008', department: '骨科', position: '护士', phone: '13800000008' },
    { staffId: '009', staffName: '郑实习生', staffCode: 'N009', department: '内科', position: '实习生', phone: '13800000009' },
    { staffId: '010', staffName: '钱护士', staffCode: 'N010', department: '外科', position: '护士', phone: '13800000010' }
  ]

  availableStaffList.value = allStaff.filter(staff => !existingStaffIds.includes(staff.staffId))
  addStaffDialogVisible.value = true
}

const handleStaffSearch = () => {
  // 根据搜索关键词过滤人员
  console.log('搜索人员:', staffSearchKeyword.value)
}

const handleStaffSelectionChange = (selection) => {
  selectedStaff.value = selection
}

const handleAddStaff = () => {
  if (selectedStaff.value.length === 0) {
    ElMessage.warning('请选择要添加的人员')
    return
  }

  selectedStaff.value.forEach(staff => {
    const newRow = { ...staff }
    // 初始化所有日期列为空
    dateColumns.value.forEach((date, index) => {
      const dayKey = `day${index + 1}`
      newRow[dayKey] = null
    })
    scheduleTableData.value.push(newRow)
  })

  addStaffDialogVisible.value = false
  selectedStaff.value = []
  ElMessage.success(`成功添加 ${selectedStaff.value.length} 名人员`)
}

// 工时统计相关
const openWorkHoursDialog = () => {
  workHoursForm.value.dateRange = [...filterForm.value.dateRange]
  workHoursDialogVisible.value = true
  nextTick(() => {
    initWorkHoursChart()
  })
}

const handleWorkHoursQuery = () => {
  initWorkHoursChart()
}

const initWorkHoursChart = () => {
  const chartDom = document.querySelector('.work-hours-chart')
  if (!chartDom) return

  const myChart = echarts.init(chartDom)

  // 模拟工时统计数据
  const staffNames = scheduleTableData.value.map(row => row.staffName)
  const workHoursData = staffNames.map(() => Math.floor(Math.random() * 200) + 100)

  const option = {
    title: {
      text: '工时统计图表'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: staffNames,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '工时(小时)'
    },
    series: [{
      name: '工时',
      type: 'bar',
      data: workHoursData,
      itemStyle: {
        color: '#409eff'
      }
    }]
  }

  myChart.setOption(option)
}

// 导入相关
const openImportDialog = () => {
  importDialogVisible.value = true
  uploadFile.value = null
}

const downloadTemplate = () => {
  ElMessage.success('模板下载功能待实现')
}

const handleFileChange = (file) => {
  uploadFile.value = file
}

const handleImport = () => {
  if (!uploadFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  // 模拟导入过程
  ElMessage.success('导入成功')
  importDialogVisible.value = false
  generateMockData()
}

// 全局点击事件处理
const handleGlobalClick = (event) => {
  // 隐藏右键菜单
  if (contextMenuVisible.value) {
    const contextMenu = document.querySelector('.context-menu')
    if (contextMenu && !contextMenu.contains(event.target)) {
      contextMenuVisible.value = false
    }
  }

  // 清除选中状态（除非按住Ctrl键）
  if (!event.ctrlKey && !event.target.closest('.schedule-cell')) {
    selectedCells.value = []
  }
}

// 键盘事件处理
const handleKeyDown = (event) => {
  // ESC键清除选中状态
  if (event.key === 'Escape') {
    selectedCells.value = []
    contextMenuVisible.value = false
  }

  // Delete键清除选中单元格的排班
  if (event.key === 'Delete' && selectedCells.value.length > 0) {
    selectedCells.value.forEach(cell => {
      const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
      if (targetRow) {
        const dayKey = `day${cell.dayIndex + 1}`
        delete targetRow[dayKey]
      }
    })
    selectedCells.value = []
    ElMessage.success('删除成功')
  }

  // Ctrl+C复制
  if (event.ctrlKey && event.key === 'c' && selectedCells.value.length === 1) {
    const cell = selectedCells.value[0]
    const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
    const dayKey = `day${cell.dayIndex + 1}`
    copiedSchedule.value = targetRow?.[dayKey] || null
    ElMessage.success('复制成功')
  }

  // Ctrl+V粘贴
  if (event.ctrlKey && event.key === 'v' && copiedSchedule.value && selectedCells.value.length > 0) {
    selectedCells.value.forEach(cell => {
      const targetRow = scheduleTableData.value.find(r => r.staffId === cell.staffId)
      if (targetRow) {
        const dayKey = `day${cell.dayIndex + 1}`
        targetRow[dayKey] = { ...copiedSchedule.value, date: cell.date }
      }
    })
    selectedCells.value = []
    ElMessage.success('粘贴成功')
  }
}

// 生命周期钩子
onMounted(() => {
  initializeData()
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style lang="scss" scoped>
.scheduling-hf-wrapper {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .filter-section {
    margin-bottom: 20px;

    .filter-card {
      .date-range-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;

        .period-nav {
          .el-button {
            padding: 5px 10px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .toolbar-section {
    margin-bottom: 20px;

    .toolbar-card {
      .toolbar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .toolbar-left {
          display: flex;
          gap: 10px;
        }

        .toolbar-right {
          .period-info {
            font-size: 14px;
            color: #666;
            font-weight: 500;
          }
        }
      }
    }
  }

  .schedule-table-section {
    .table-card {
      .table-header {
        padding: 0 0 15px 0;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 15px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
      }

      .table-wrapper {
        overflow-x: auto;

        :deep(.el-table) {
          .weekend-column {
            background-color: #fdf6ec !important;
          }

          .weekend-cell {
            background-color: #fdf6ec !important;
          }

          .date-header {
            text-align: center;

            .date-day {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
            }

            .date-week {
              font-size: 12px;
              color: #909399;
              margin-top: 2px;
            }
          }

          .staff-info {
            text-align: center;

            .staff-name {
              font-size: 14px;
              font-weight: 600;
              color: #303133;
            }

            .staff-code {
              font-size: 12px;
              color: #909399;
              margin-top: 2px;
            }
          }

          .schedule-cell {
            position: relative;
            min-height: 60px;
            padding: 5px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;

            &:hover {
              background-color: #f0f9ff;
            }

            &.has-schedule {
              background-color: #e1f3d8;

              &:hover {
                background-color: #d1e7dd;
              }
            }

            &.conflict {
              background-color: #fef0f0;
              border: 1px dashed #f56c6c;

              &:hover {
                background-color: #fde2e2;
              }
            }

            &.selected {
              background-color: #e6f7ff;
              border: 2px solid #409eff;
            }

            .schedule-content {
              .shift-info {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 2px;

                .shift-name {
                  font-size: 12px;
                  font-weight: 600;
                  color: #303133;
                  padding: 2px 6px;
                  background-color: #409eff;
                  color: white;
                  border-radius: 10px;
                }

                .area-name {
                  font-size: 11px;
                  color: #666;
                }
              }
            }

            .conflict-indicator {
              position: absolute;
              top: 2px;
              right: 2px;

              .conflict-icon {
                color: #f56c6c;
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  // 弹窗样式
  :deep(.el-dialog) {
    .dialog-footer {
      text-align: right;
    }

    .selected-cells-info {
      padding: 10px;
      background-color: #f0f9ff;
      border-radius: 4px;
      color: #409eff;
      font-size: 14px;
    }

    .add-staff-content {
      .staff-search {
        margin-bottom: 15px;
      }

      .staff-table {
        .el-table {
          border: 1px solid #ebeef5;
        }
      }
    }

    .work-hours-content {
      .work-hours-filter {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #ebeef5;
      }

      .work-hours-chart {
        width: 100%;
        height: 400px;
      }
    }

    .import-content {
      .import-tips {
        margin-bottom: 20px;

        :deep(.el-alert) {
          .el-alert__content {
            p {
              margin: 5px 0;
              line-height: 1.5;
            }
          }
        }
      }

      .import-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
      }

      .upload-file-info {
        padding: 10px;
        background-color: #f0f9ff;
        border-radius: 4px;
        color: #409eff;

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }

  // 右键菜单样式
  .context-menu {
    position: fixed;
    background: white;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 9999;
    min-width: 120px;

    .menu-item {
      padding: 10px 15px;
      cursor: pointer;
      font-size: 14px;
      color: #606266;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      &:first-child {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
      }

      &:last-child {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
      }

      &:not(:last-child) {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }
}

// 全局样式覆盖
:deep(.el-table__cell) {
  padding: 8px 0;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-card__body) {
  padding: 15px;
}

// 响应式设计
@media (max-width: 1200px) {
  .scheduling-hf-wrapper {
    .filter-section {
      .filter-card {
        :deep(.el-form) {
          .el-row {
            .el-col {
              margin-bottom: 10px;
            }
          }
        }
      }
    }

    .schedule-table-section {
      .table-card {
        .table-wrapper {
          :deep(.el-table) {
            .schedule-cell {
              min-height: 50px;

              .schedule-content {
                .shift-info {
                  .shift-name {
                    font-size: 11px;
                    padding: 1px 4px;
                  }

                  .area-name {
                    font-size: 10px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .scheduling-hf-wrapper {
    padding: 10px;

    .filter-section,
    .toolbar-section {
      margin-bottom: 15px;
    }

    .schedule-table-section {
      .table-card {
        .table-wrapper {
          :deep(.el-table) {
            font-size: 12px;

            .schedule-cell {
              min-height: 40px;
              padding: 3px;

              .schedule-content {
                .shift-info {
                  .shift-name {
                    font-size: 10px;
                    padding: 1px 3px;
                  }

                  .area-name {
                    font-size: 9px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>