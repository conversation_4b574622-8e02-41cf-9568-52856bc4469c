<template>
  <div class="buildmanage-page">
    <div class="page-container">
      <!-- 左侧树形结构 -->
      <div class="left-panel">
        <div class="panel-header">
          <span class="title">楼栋信息</span>
          <el-button type="primary" @click="showAddBuilding = true">新增楼栋</el-button>
        </div>
        <el-tree
          :data="buildingData"
          :props="defaultProps"
          node-key="id"
          default-expand-all
          highlight-current
          :default-expanded-keys="['1', '1-1']"
          :current-key="'1-1-1'"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="node-content">
                <el-icon :size="16" :color="getNodeIconColor(data)" style="margin-right: 4px">
                  <OfficeBuilding v-if="data.type === 'building'" />
                  <Grid v-else-if="data.type === 'floor'"/>
                  <HomeFilled v-else-if="data.type === 'room'" />
                </el-icon>
                <span>{{ node.label }}</span>
              </div>
              <span class="operation-btns" v-if="data.type !== 'room'">
                <el-button link type="primary" @click.stop="handleAdd(node, data)">添加</el-button>
                <el-button link type="primary"  @click.stop="handleEdit(node, data)">编辑</el-button>
                <el-button link type="danger"  @click.stop="handleDelete(node, data)">删除</el-button>
              </span>
            </div>
          </template>
        </el-tree>
        <!-- 新增楼栋弹窗 -->
        <el-dialog
          v-model="showAddBuilding"
          title="新增楼栋"
          width="400px"
          :close-on-click-modal="false"
        >
          <el-form :model="addBuildingForm" :rules="buildingRules" ref="buildingFormRef" label-width="80px">
            <el-form-item label="楼栋名称" prop="buildingName">
              <el-input v-model="addBuildingForm.buildingName" placeholder="请输入楼栋名称" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="showAddBuilding = false">取消</el-button>
            <el-button type="primary" @click="handleAddBuilding">确定</el-button>
          </template>
        </el-dialog>

        <!-- 编辑楼栋弹窗 -->
        <el-dialog
          v-model="showEditBuilding"
          title="编辑楼栋"
          width="400px"
          :close-on-click-modal="false"
        >
          <el-form :model="editBuildingForm" :rules="buildingRules" ref="buildingFormRef" label-width="80px">
            <el-form-item label="楼栋名称" prop="buildingName">
              <el-input v-model="editBuildingForm.buildingName" placeholder="请输入楼栋名称" />
            </el-form-item>
          </el-form>

          <template #footer>
            <el-button @click="showEditBuilding = false">取消</el-button>
            <el-button type="primary" @click="handleEditBuildingSubmit">确定</el-button>
          </template>
        </el-dialog>
        <!-- 编辑楼层弹窗 -->
        <el-dialog
          v-model="showEditFloor"
          title="编辑楼层"
          width="400px"
          :close-on-click-modal="false"
        >
          <el-form :model="editFloorForm" :rules="floorRules" ref="floorFormRef" label-width="100px">
            <el-form-item label="楼层名称" prop="floorName">
              <el-input v-model="editFloorForm.floorName" placeholder="请输入楼层名称" />
            </el-form-item>
            <el-form-item label="楼层号" prop="floorNumber">
              <el-input-number v-model="editFloorForm.floorNumber" :min="1" style="width: 100%" placeholder="请输入楼层号" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="editFloorForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="showEditFloor = false">取消</el-button>
            <el-button type="primary" @click="handleEditFloorSubmit">确定</el-button>
          </template>
        </el-dialog>

        <!-- 新增楼层弹窗 -->
        <el-dialog
          v-model="showAddFloor"
          title="新增楼层"
          width="400px"
          :close-on-click-modal="false"
        >
          <el-form :model="addFloorForm" :rules="floorRules" ref="floorFormRef" label-width="100px">
            <el-form-item label="楼层名称" prop="floorName">
              <el-input v-model="addFloorForm.floorName" placeholder="请输入楼层名称" />
            </el-form-item>
            <el-form-item label="楼层号" prop="floorNumber">
              <el-input-number v-model="addFloorForm.floorNumber" :min="1" style="width: 100%" placeholder="请输入楼层号" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="addFloorForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="showAddFloor = false">取消</el-button>
            <el-button type="primary" @click="handleAddFloorSubmit">确定</el-button>
          </template>
        </el-dialog>

        <!-- 新增房间弹窗 -->
        <el-dialog
          v-model="showAddRoom"
          title="新增房间"
          width="500px"
          :close-on-click-modal="false"
        >
          <el-form :model="addRoomForm" :rules="roomRules" ref="roomFormRef" label-width="100px">
            <el-form-item label="房间号" prop="roomNumber">
              <el-input v-model="addRoomForm.roomNumber" placeholder="请输入房间号" />
            </el-form-item>
            <el-form-item label="房间名称" prop="roomName">
              <el-input v-model="addRoomForm.roomName" placeholder="请输入房间名称" />
            </el-form-item>
            <el-form-item label="房间类型" prop="roomType">
              <el-select v-model="addRoomForm.roomType" placeholder="请选择房间类型" style="width: 100%">
                <el-option label="单人间" value="单人间" />
                <el-option label="双人间" value="双人间" />
                <el-option label="三人间" value="三人间" />
                <el-option label="多人间" value="多人间" />
              </el-select>
            </el-form-item>
            <el-form-item label="朝向" prop="roomOrientation">
              <el-select v-model="addRoomForm.roomOrientation" placeholder="请选择房间朝向" style="width: 100%">
                <el-option label="东" value="东" />
                <el-option label="南" value="南" />
                <el-option label="西" value="西" />
                <el-option label="北" value="北" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
            <el-form-item label="面积" prop="roomArea">
              <el-input-number v-model="addRoomForm.roomArea" :min="1" :precision="0" style="width: 100%" placeholder="请输入房间面积" />
            </el-form-item>
            <el-form-item label="区域" prop="areaName">
  <el-select v-model="addRoomForm.areaName" placeholder="请选择区域类型" style="width: 100%">
    <el-option label="自理区" value="自理区" />
    <el-option label="介助区" value="介助区" />
    <el-option label="介护区" value="介护区" /> 
    <el-option label="其他区" value="其他区" /> 
  </el-select>
</el-form-item>
<el-form-item label="设施" prop="roomFacilities">
              <el-select v-model="addRoomForm.roomFacilities" multiple placeholder="请选择房间设施" style="width: 100%">
                <el-option label="电视" value="电视" />
                <el-option label="空调" value="空调" />
                <el-option label="床头柜" value="床头柜" />
                <el-option label="卫生间" value="卫生间" />
                <el-option label="护理床" value="护理床" />
              </el-select>
            </el-form-item>
            <el-form-item label="床位" prop="capacity">
              <el-input-number v-model="addRoomForm.capacity" :min="1" :precision="0" style="width: 100%" placeholder="请输入床位容量" />
            </el-form-item>
            <el-form-item label="负责人" prop="managerName">
              <el-input v-model="addRoomForm.managerName" placeholder="请输入负责人姓名" />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="addRoomForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="showAddRoom = false">取消</el-button>
            <el-button type="primary" @click="handleAddRoomSubmit">确定</el-button>
          </template>
        </el-dialog>
      </div>

      <!-- 右侧房间详情 -->
      <div class="right-panel" v-if="currentRoom">
        <div class="panel-header">
          <span class="title">房间详情</span>
          <div>
            <el-button type="primary"  @click="handleEditRoom">编辑</el-button>
            <el-button type="danger"  @click="handleDeleteRoom">删除</el-button>
          </div>
        </div>
        <div class="room-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="房间号">{{ currentRoom.roomNo }}</el-descriptions-item>
            <el-descriptions-item label="房间类型">{{ currentRoom.roomType || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="区域">{{ currentRoom.zone }}</el-descriptions-item>
            <el-descriptions-item label="朝向">{{ currentRoom.direction }}</el-descriptions-item>
            <el-descriptions-item label="面积">{{ currentRoom.area }}㎡</el-descriptions-item>
            <el-descriptions-item label="状态">{{ currentRoom.status || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="设施" :span="2">
              <el-tag v-for="item in currentRoom.facilities" :key="item" size="small" style="margin-right: 4px">
                {{ item }}
              </el-tag>
              <span v-if="!currentRoom.facilities || currentRoom.facilities.length === 0">无</span>
            </el-descriptions-item>
            <el-descriptions-item label="床位容量" :span="2">{{ currentRoom.capacity || currentRoom.bedCapacity || 0 }}</el-descriptions-item>
            <el-descriptions-item label="负责人" :span="2">{{ currentRoom.managerName || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ currentRoom.remark || '无' }}</el-descriptions-item>
          </el-descriptions>

          <!-- 床位信息表格 -->
          <div class="bed-info" style="margin-top: 20px">
            <div class="section-header" style="margin-bottom: 16px">
              <span class="title">床位信息</span>
              <el-button type="primary"  @click="handleAddBed">添加床位</el-button>
            </div>
            <el-table :data="currentRoom.beds" border style="width: 100%">
              <el-table-column prop="bedNo" label="床位号" width="100" />
              <el-table-column prop="bedCode" label="床位编码" width="120" />
              <el-table-column prop="type" label="床位类型" width="100" />
              <el-table-column prop="price" label="床位价格" width="100">
                <template #default="{ row }">
                  {{ row.price || 0 }}元/月
                </template>
              </el-table-column>
              <el-table-column prop="elderId" label="入住状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.elderId ? 'success' : 'info'">
                    {{ row.elderId ? '已入住' : '空闲' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" />
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button link type="primary"  @click="handleEditBed(row)">编辑</el-button>
                  <el-button link type="danger"  @click="handleDeleteBed(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="right-panel empty-panel" v-else>
        <el-empty description="请选择房间查看详情" />
      </div>
    </div>

    <!-- 新增床位弹框 -->
    <el-dialog
      v-model="showAddBedDialog"
      title="新增床位"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="addBedForm" :rules="bedRules" ref="bedFormRef" label-width="100px">
        <el-form-item label="床位号" prop="bedNo">
          <el-input v-model="addBedForm.bedNo" placeholder="请输入床位号" />
        </el-form-item>
        <el-form-item label="床位编码" prop="bedCode">
          <el-input v-model="addBedForm.bedCode" placeholder="请输入床位编码" />
        </el-form-item>
        <el-form-item label="床位价格" prop="price">
          <el-input-number v-model="addBedForm.price" :min="0" :precision="0" style="width: 100%" placeholder="请输入床位价格" />
        </el-form-item>
        <el-form-item label="床位类型" prop="type">
          <el-select v-model="addBedForm.type" placeholder="请选择床位类型" style="width: 100%">
            <el-option label="普通床" value="普通床" />
            <el-option label="医疗床" value="医疗床" />
            <el-option label="护理床" value="护理床" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="addBedForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddBedDialog = false">取消</el-button>
        <el-button type="primary" @click="submitAddBed">确定</el-button>
      </template>
    </el-dialog>

    <!-- 编辑床位弹框 -->
    <el-dialog
      v-model="showEditBedDialog"
      title="编辑床位"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="editBedForm" :rules="bedRules" ref="editBedFormRef" label-width="100px">
        <el-form-item label="床位号" prop="bedNo">
          <el-input v-model="editBedForm.bedNo" placeholder="请输入床位号" />
        </el-form-item>
        <el-form-item label="床位编码" prop="bedCode">
          <el-input v-model="editBedForm.bedCode" placeholder="请输入床位编码" />
        </el-form-item>
        <el-form-item label="床位价格" prop="price">
          <el-input-number v-model="editBedForm.price" :min="0" :precision="0" style="width: 100%" placeholder="请输入床位价格" />
        </el-form-item>
        <el-form-item label="床位类型" prop="type">
          <el-select v-model="editBedForm.type" placeholder="请选择床位类型" style="width: 100%">
            <el-option label="普通床" value="普通床" />
            <el-option label="医疗床" value="医疗床" />
            <el-option label="护理床" value="护理床" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editBedForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditBedDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditBedSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 编辑房间弹框 -->
    <el-dialog
      v-model="showEditRoomDialog"
      title="编辑房间"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="editRoomForm" :rules="roomRules" ref="editRoomFormRef" label-width="100px">
        <el-form-item label="房间号" prop="roomNumber">
          <el-input v-model="editRoomForm.roomNumber" placeholder="请输入房间号" />
        </el-form-item>
        <el-form-item label="房间名称" prop="roomName">
          <el-input v-model="editRoomForm.roomName" placeholder="请输入房间名称" />
        </el-form-item>
        <el-form-item label="房间类型" prop="roomType">
          <el-select v-model="editRoomForm.roomType" placeholder="请选择房间类型" style="width: 100%">
            <el-option label="单人间" value="单人间" />
            <el-option label="双人间" value="双人间" />
            <el-option label="三人间" value="三人间" />
            <el-option label="多人间" value="多人间" />
          </el-select>
        </el-form-item>
        <el-form-item label="朝向" prop="roomOrientation">
          <el-select v-model="editRoomForm.roomOrientation" placeholder="请选择房间朝向" style="width: 100%">
            <el-option label="东" value="东" />
            <el-option label="南" value="南" />
            <el-option label="西" value="西" />
            <el-option label="北" value="北" />
          </el-select>
        </el-form-item>
        <el-form-item label="面积" prop="roomArea">
          <el-input-number v-model="editRoomForm.roomArea" :min="1" :precision="0" style="width: 100%" placeholder="请输入房间面积" />
        </el-form-item>
        <el-form-item label="区域" prop="areaName">
          <el-select v-model="editRoomForm.areaName" placeholder="请选择区域类型" style="width: 100%">
            <el-option label="自理区" value="自理区" />
            <el-option label="介助区" value="介助区" />
            <el-option label="照护区" value="照护区" />
          </el-select>
        </el-form-item>
        <el-form-item label="设施" prop="roomFacilities">
          <el-select v-model="editRoomForm.roomFacilities" multiple placeholder="请选择房间设施" style="width: 100%">
            <el-option label="电视" value="电视" />
            <el-option label="空调" value="空调" />
            <el-option label="床头柜" value="床头柜" />
            <el-option label="卫生间" value="卫生间" />
            <el-option label="护理床" value="护理床" />
          </el-select>
        </el-form-item>
        <el-form-item label="床位容量" prop="capacity">
          <el-input-number v-model="editRoomForm.capacity" :min="1" :precision="0" style="width: 100%" placeholder="请输入床位容量" />
        </el-form-item>
        <el-form-item label="负责人" prop="managerName">
          <el-input v-model="editRoomForm.managerName" placeholder="请输入负责人姓名" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="editRoomForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditRoomDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditRoomSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Grid, OfficeBuilding, HomeFilled } from '@element-plus/icons-vue'
import { getRoomTreeList, addBuildingInfo, updateBuildingInfo, deleteBuildingInfo, addFloorInfo, updateFloorInfo, deleteFloorInfo,addRoomInfo,updateRoomInfo,getRoomInfo,deleteRoomInfo,addBedInfo,updateBedInfo,deleteBedInfo,listBed } from '@/api/live/buildmanage'

// 树形数据
const buildingData = ref([])

// 获取房间树形数据
const fetchRoomTreeData = async () => {
  try {
    const res = await getRoomTreeList()
    if (res.code === 200) {
      buildingData.value = res.data
    }
  } catch (error) {
    console.error('获取房间树形数据失败:', error)
    ElMessage.error('获取楼栋信息失败')
  }
}

const defaultProps = {
  children: 'children',
  label: 'label'
}

// 当前选中的房间信息
const currentRoom = ref(null)

// 页面加载时获取数据并自动选中第一个房间
onMounted(async () => {
  await fetchRoomTreeData()
  if (buildingData.value.length > 0 && buildingData.value[0].children?.length > 0 && buildingData.value[0].children[0].children?.length > 0) {
    const firstBuilding = buildingData.value[0]
    const firstFloor = firstBuilding.children[0]
    const firstRoom = firstFloor.children[0]

    // 设置当前楼栋和楼层节点
    currentBuildingNode.value = firstBuilding
    currentFloorNode.value = firstFloor

    // 调用handleNodeClick并传递模拟的node参数
    handleNodeClick(firstRoom, {
      parent: {
        data: firstFloor,
        parent: {
          data: firstBuilding
        }
      }
    })
  }
})

// 表单引用
const buildingFormRef = ref(null)
const floorFormRef = ref(null)
const roomFormRef = ref(null)

// 新增楼栋弹窗
const showAddBuilding = ref(false)
const addBuildingForm = ref({
  buildingName: ''
})
const buildingRules = {
  buildingName: [{ required: true, message: '请输入楼栋名称', trigger: 'blur' }]
}

// 新增楼层弹窗
const showAddFloor = ref(false)
const addFloorForm = ref({
  buildingId: '',
  floorName: '',
  floorNumber: '',
  remark: ''
})
const floorRules = {
  floorName: [{ required: true, message: '请输入楼层名称', trigger: 'blur' }],
  floorNumber: [{ required: true, message: '请输入楼层号', trigger: 'blur' }]
}

// 新增房间弹窗
const showAddRoom = ref(false)
const addRoomForm = ref({
  roomNumber: '',
  roomName: '',
  roomType: '单人间',
  roomOrientation: '东',
  roomArea: 25,
  roomFacilities: ['电视', '空调', '床头柜'],
  areaName: '自理区',
  capacity: 1,
  managerName: '',
  managerCode: '',
  remark: ''
})
const roomRules = {
  roomNumber: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
  roomName: [{ required: true, message: '请输入房间名称', trigger: 'blur' }],
  roomType: [{ required: true, message: '请选择房间类型', trigger: 'change' }],
  roomOrientation: [{ required: true, message: '请选择房间朝向', trigger: 'change' }],
  roomArea: [{ required: true, message: '请输入房间面积', trigger: 'blur' }],
  roomFacilities: [{ required: true, message: '请选择房间设施', trigger: 'change' }],
  areaName: [{ required: true, message: '请选择区域类型', trigger: 'change' }],
  capacity: [{ required: true, message: '请输入床位容量', trigger: 'blur' }]
}

// 节点类型判断
function isBuilding(data) {
  return !data.type && !data.parent
}

function isFloor(data) {
  return !data.type && data.parent
}

function getNodeIconColor(data) {
  return '#409EFF'
}

// 点击树节点
async function handleNodeClick(data, node) {
  if (data.type === 'room') {
    // 先设置基本信息，避免界面闪烁
    currentRoom.value = data

    // 设置当前楼层节点和楼栋节点，用于编辑和删除操作
    if (node && node.parent) {
      currentFloorNode.value = node.parent.data
      if (node.parent.parent) {
        currentBuildingNode.value = node.parent.parent.data
      }
    }

    try {
      // 调用查询房间接口获取详细信息
      const roomRes = await getRoomInfo(data.id)
      if (roomRes.code === 200 && roomRes.data) {
        // 更新房间详情信息
        const roomInfo = roomRes.data
        // 保留原有的beds数据，避免影响床位加载
        const originalBeds = currentRoom.value.beds || []

        // 更新房间信息
        currentRoom.value = {
          ...data,
          roomNo: roomInfo.roomNumber,
          roomType: roomInfo.roomType,
          direction: roomInfo.roomOrientation,
          area: roomInfo.roomArea,
          zone: roomInfo.areaName,
          facilities: roomInfo.roomFacilities ? roomInfo.roomFacilities.split(',') : [],
          remark: roomInfo.remark,
          status: roomInfo.status,
          beds: originalBeds
        }
      }

      // 加载房间的床位信息
      await fetchRoomBeds(data.id)
    } catch (error) {
      console.error('获取房间详情失败:', error)
      ElMessage.error('获取房间详情失败')
      // 加载房间的床位信息
      await fetchRoomBeds(data.id)
    }
  } else if (data.type === 'floor') {
    // 设置当前楼层节点
    currentFloorNode.value = data
    if (node && node.parent) {
      currentBuildingNode.value = node.parent.data
    }
    currentRoom.value = null
  } else if (data.type === 'building') {
    // 设置当前楼栋节点
    currentBuildingNode.value = data
    currentFloorNode.value = null
    currentRoom.value = null
  } else {
    currentRoom.value = null
  }
}

// 获取房间的床位信息
async function fetchRoomBeds(roomId) {
  try {
    const res = await listBed({ roomId })
    if (res.code === 200) {
      // 将API返回的床位数据转换为页面需要的格式
      const beds = res.rows.map(bed => ({
        id: bed.id,
        bedNo: bed.bedNumber,
        bedCode: bed.bedCode,
        type: bed.bedType,
        price: bed.bedPrice || 0, // 使用API返回的价格字段，如果没有则默认为0
        remark: bed.remark,
        elderId: bed.elderId
      }))
      // 更新当前房间的床位信息
      currentRoom.value.beds = beds
    } else {
      ElMessage.error(res.msg || '获取床位信息失败')
    }
  } catch (error) {
    console.error('获取床位信息失败:', error)
    ElMessage.error('获取床位信息失败')
  }
}

// 新增楼栋
async function handleAddBuilding() {
  buildingFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await addBuildingInfo({
  buildingName: addBuildingForm.value.buildingName
})
        if (res.code === 200) {
          buildingData.value.push({
            id: res.data.id,
            label: res.data.buildingName || res.data.name,
            type: 'building',
            children: []
          })
          ElMessage.success('添加成功')
          showAddBuilding.value = false
          addBuildingForm.value = {
          buildingName: ''
        }
          buildingFormRef.value.resetFields()
        } else {
          ElMessage.error(res.message || '添加失败')
        }
      } catch (error) {
        console.error('添加楼栋失败:', error)
        if (error.response?.status === 200) {
          ElMessage.success('添加成功')
        } else {
          ElMessage.error('添加楼栋失败')
        }
      }
    }
  })
}

// 添加子节点
function handleAdd(node, data) {
  if (data.type === 'building') {
    showAddFloor.value = true
    addFloorForm.value.name = ''
    // 保存当前选中的楼栋节点
    currentBuildingNode.value = data
  } else if (data.type === 'floor') {
    showAddRoom.value = true
    // Object.assign(addRoomForm.value, {

    //   direction: '东',
    //   area: 25,
    //   facilities: ['电视', '空调', '床头柜'],
    //   remark: ''
    // })
    // 保存当前选中的楼层节点
    currentFloorNode.value = data
    // 确保当前楼栋节点也被设置
    currentBuildingNode.value = node.parent.data
  }
  // else if (data.type === 'room') {
  //   // 保存当前选中的房间节点
  //   currentRoomNode.value = data
  //   console.log("currentRoomNode",data)
  // }
}

// 新增楼层提交
async function handleAddFloorSubmit() {
  floorFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await addFloorInfo({
          buildingId: currentBuildingNode.value.id,
          floorName: addFloorForm.value.floorName,
          floorNumber: addFloorForm.value.floorNumber,
          remark: addFloorForm.value.remark
        })
        if (res.code === 200) {
          currentBuildingNode.value.children.push({
            id: res.data.id,
            label: res.data.floorName,
            type: 'floor',
            children: []
          })
          ElMessage.success('添加成功')
          showAddFloor.value = false
          addFloorForm.value = {
            buildingId: '',
            floorName: '',
            floorNumber: '',
            remark: ''
          }
          floorFormRef.value.resetFields()
        } else {
          ElMessage.error(res.message || '添加失败')
        }
      } catch (error) {
        console.error('添加楼层失败:', error)
        ElMessage.error('添加楼层失败')
      }
    }
  })
}

// 新增房间提交
async function handleAddRoomSubmit() {
  roomFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 确保负责人编码与姓名一致
        addRoomForm.value.managerCode = addRoomForm.value.managerName
        
        // 将roomFacilities数组转换为逗号分隔的字符串
        const facilities = Array.isArray(addRoomForm.value.roomFacilities)
          ? addRoomForm.value.roomFacilities.join(',')
          : '';

        const res = await addRoomInfo({
          buildingId: currentBuildingNode.value.id,
          floorId: currentFloorNode.value.id,
          roomName: addRoomForm.value.roomName,
          roomNumber: addRoomForm.value.roomNumber,
          roomType: addRoomForm.value.roomType,
          roomOrientation: addRoomForm.value.roomOrientation,
          roomArea: addRoomForm.value.roomArea,
          roomFacilities: facilities,
          areaName: addRoomForm.value.areaName,
          capacity: addRoomForm.value.capacity,
          managerName: addRoomForm.value.managerName,
          managerCode: addRoomForm.value.managerCode,
          remark: addRoomForm.value.remark}
        )

        if (res.code === 200) {
          currentFloorNode.value.children.push({
            id: res.data.id,
            label: addRoomForm.value.roomName,
            type: 'room',
            roomNo: addRoomForm.value.roomNumber,
            roomType: addRoomForm.value.roomType,
            direction: addRoomForm.value.roomOrientation,
            area: addRoomForm.value.roomArea,
            facilities: facilities,
            remark: addRoomForm.value.remark,
            beds: [],
            zone: addRoomForm.value.areaName,
            capacity: addRoomForm.value.capacity,
            managerName: addRoomForm.value.managerName,
            status: '空闲'
          })
          ElMessage.success('添加成功')
          showAddRoom.value = false
          roomFormRef.value.resetFields()
        } else {
          ElMessage.error(res.message || '添加失败')
        }
      } catch (error) {
        console.error('添加房间失败:', error)
        ElMessage.error('添加房间失败')
      }
    }
  })
}

// 当前选中的节点
const currentBuildingNode = ref(null)
const currentFloorNode = ref(null)

// 编辑楼栋弹窗
const showEditBuilding = ref(false)
const editBuildingForm = ref({
  id: '',
  buildingName: ''
})

// 编辑楼层弹窗
const showEditFloor = ref(false)
const editFloorForm = ref({
  id: '',
  buildingId: '',
  floorName: '',
  floorNumber: '',
  remark: ''
})

// 编辑节点
function handleEdit(node, data) {
  console.log(node, data, showEditFloor.value, showEditBuilding.value);
  if (data.type === 'building') {
    showEditFloor.value = false
    showEditBuilding.value = true
    editBuildingForm.value = {
      id: data.id,
      buildingName: data.label
    }
  } else if (data.type === 'floor') {
    showEditFloor.value = true
    showEditBuilding.value = false
    console.log(showEditFloor.value, showEditBuilding.value);
    editFloorForm.value = {
      id: data.id,
      buildingId: node.parent.data.id,
      floorName: data.label,
      floorNumber: data.floorNumber,
      remark: data.remark
    }
  }
}

// 编辑楼层
async function handleEditFloor(node, data) {
  try {
    const res = await updateFloorInfo({
      id: data.id,
      buildingId: data.parent.id,
      floorName: data.label,
      floorNumber: data.floorNumber,
      remark: data.remark
    })

    if (res?.code === 200) {
      ElMessage.success('更新成功')
      data.label = res.data.floorName
      data.floorNumber = res.data.floorNumber
      data.remark = res.data.remark
    } else {
      ElMessage.error(res?.message || '更新失败')
    }
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error(error.message || '更新失败')
  }
}

// 提交编辑楼栋
async function handleEditBuildingSubmit() {
  buildingFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await updateBuildingInfo({
          id: editBuildingForm.value.id,
          buildingName: editBuildingForm.value.buildingName
        })

        if (res?.code === 200) {
          // 更新树节点数据
          const node = buildingData.value.find(n => n.id === editBuildingForm.value.id)
          if (node) {
            node.label = editBuildingForm.value.buildingName
          }
          ElMessage.success('更新成功')
          showEditBuilding.value = false
          // 重置表单
          editBuildingForm.value = {
            id: '',
            buildingName: ''
          }
          buildingFormRef.value.resetFields()
        } else {
          ElMessage.error(res?.message || '更新失败')
        }
      } catch (error) {
        console.error('更新失败:', error)
        ElMessage.error(error.message || '更新失败')
      }
    }
  })
}

// 编辑楼层提交
async function handleEditFloorSubmit() {
  floorFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await updateFloorInfo(editFloorForm.value)
        if (res.code === 200) {
          const building = buildingData.value.find(item => item.id === editFloorForm.value.buildingId)
          if (building) {
            const floor = building.children.find(item => item.id === editFloorForm.value.id)
            if (floor) {
              floor.label = editFloorForm.value.floorName
              floor.floorNumber = editFloorForm.value.floorNumber
              floor.remark = editFloorForm.value.remark
            }
          }
          ElMessage.success('编辑成功')
          showEditFloor.value = false
        } else {
          ElMessage.error(res.message || '编辑失败')
        }
      } catch (error) {
        console.error('编辑楼层失败:', error)
        ElMessage.error('编辑楼层失败')
      }
    }
  })
}


// 删除节点
async function handleDelete(node, data) {
  try {
    await ElMessageBox.confirm('确认删除该节点吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    let res
    if (data.type === 'building') {
      res = await deleteBuildingInfo(data.id)
    } else if (data.type === 'floor') {
      res = await deleteFloorInfo(data.id)
    }

    if (res?.code === 200) {
      // 更新树节点数据
      if (data.type === 'building') {
        const index = buildingData.value.findIndex(b => b.id === data.id)
        if (index > -1) {
          buildingData.value.splice(index, 1)
        }
      } else {
        const parent = node.parent
        const children = parent.data.children || []
        const index = children.findIndex(d => d.id === data.id)
        if (index > -1) {
          children.splice(index, 1)
        }
      }

      // 如果删除的是当前选中的房间，清空房间信息
      if (currentRoom.value?.id === data.id) {
        currentRoom.value = null
      }
      ElMessage.success('删除成功')
    } else {
      ElMessage.error(res?.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}

// 编辑房间弹框
const showEditRoomDialog = ref(false)
const editRoomFormRef = ref(null)
const editRoomForm = ref({
  id: '',
  buildingId: '',
  floorId: '',
  roomNumber: '',
  roomName: '',
  roomType: '',
  roomOrientation: '',
  roomArea: 0,
  areaName: '',
  roomFacilities: [],
  capacity: 1,
  status: '',
  managerName: '',
  managerCode: '',
  remark: ''
})

// 编辑房间信息
function handleEditRoom() {
  if (!currentRoom.value) return

  // 将当前房间信息填充到表单中
  editRoomForm.value = {
    id: currentRoom.value.id,
    buildingId: currentBuildingNode.value ? currentBuildingNode.value.id : '',
    floorId: currentFloorNode.value ? currentFloorNode.value.id : '',
    roomNumber: currentRoom.value.roomNo,
    roomName: currentRoom.value.label,
    roomType: currentRoom.value.roomType || '',
    roomOrientation: currentRoom.value.direction || '',
    roomArea: currentRoom.value.area || 0,
    areaName: currentRoom.value.zone || '',
    roomFacilities: currentRoom.value.facilities || [],
    capacity: currentRoom.value.capacity || 1,
    status: currentRoom.value.status || '空闲',
    managerName: currentRoom.value.managerName || '',
    managerCode: currentRoom.value.managerName || '',
    remark: currentRoom.value.remark || ''
  }

  showEditRoomDialog.value = true
}

// 提交编辑房间
async function handleEditRoomSubmit() {
  editRoomFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 确保负责人编码与姓名一致
        editRoomForm.value.managerCode = editRoomForm.value.managerName
        
        // 准备提交到API的数据
        const roomData = {
          id: editRoomForm.value.id,
          buildingId: editRoomForm.value.buildingId,
          floorId: editRoomForm.value.floorId,
          roomNumber: editRoomForm.value.roomNumber,
          roomName: editRoomForm.value.roomName,
          roomType: editRoomForm.value.roomType,
          roomOrientation: editRoomForm.value.roomOrientation,
          roomArea: editRoomForm.value.roomArea,
          areaName: editRoomForm.value.areaName,
          roomFacilities: editRoomForm.value.roomFacilities.join(','),
          capacity: editRoomForm.value.capacity,
          status: editRoomForm.value.status,
          managerName: editRoomForm.value.managerName,
          managerCode: editRoomForm.value.managerCode,
          remark: editRoomForm.value.remark
        }

        // 调用API更新房间
        const res = await updateRoomInfo(roomData)

        if (res.code === 200) {
          // 更新树节点信息
          if (currentFloorNode.value && currentFloorNode.value.children) {
            const roomIndex = currentFloorNode.value.children.findIndex(item => item.id === currentRoom.value.id)
            if (roomIndex !== -1) {
              // 更新树节点中的房间信息
              currentFloorNode.value.children[roomIndex].label = editRoomForm.value.roomName
              currentFloorNode.value.children[roomIndex].roomNo = editRoomForm.value.roomNumber
              currentFloorNode.value.children[roomIndex].roomType = editRoomForm.value.roomType
              currentFloorNode.value.children[roomIndex].direction = editRoomForm.value.roomOrientation
              currentFloorNode.value.children[roomIndex].area = editRoomForm.value.roomArea
              currentFloorNode.value.children[roomIndex].zone = editRoomForm.value.areaName
              currentFloorNode.value.children[roomIndex].facilities = editRoomForm.value.roomFacilities
              currentFloorNode.value.children[roomIndex].capacity = editRoomForm.value.capacity
              currentFloorNode.value.children[roomIndex].status = editRoomForm.value.status
              currentFloorNode.value.children[roomIndex].managerName = editRoomForm.value.managerName
              currentFloorNode.value.children[roomIndex].managerCode = editRoomForm.value.managerCode
              currentFloorNode.value.children[roomIndex].remark = editRoomForm.value.remark
            }
          }

          // 更新当前房间信息
          currentRoom.value.label = editRoomForm.value.roomName
          currentRoom.value.roomNo = editRoomForm.value.roomNumber
          currentRoom.value.roomType = editRoomForm.value.roomType
          currentRoom.value.direction = editRoomForm.value.roomOrientation
          currentRoom.value.area = editRoomForm.value.roomArea
          currentRoom.value.zone = editRoomForm.value.areaName
          currentRoom.value.facilities = editRoomForm.value.roomFacilities
          currentRoom.value.capacity = editRoomForm.value.capacity
          currentRoom.value.status = editRoomForm.value.status
          currentRoom.value.managerName = editRoomForm.value.managerName
          currentRoom.value.managerCode = editRoomForm.value.managerCode
          currentRoom.value.remark = editRoomForm.value.remark

          ElMessage.success('修改成功')
          showEditRoomDialog.value = false
        } else {
          ElMessage.error(res.msg || '修改失败')
        }
      } catch (error) {
        console.error('修改房间失败:', error)
        ElMessage.error('修改房间失败')
      }
    }
  })
}

// 删除房间
function handleDeleteRoom() {
  if (!currentRoom.value) return

  // 检查房间中是否有床位已入住
  const hasOccupiedBeds = currentRoom.value.beds && currentRoom.value.beds.some(bed => bed.elderId)
  if (hasOccupiedBeds) {
    ElMessage.error('该房间中有床位已有老人入住，不允许删除')
    return
  }

  ElMessageBox.confirm('确认删除该房间吗？删除后将无法恢复，且会同时删除房间内的所有床位。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用API删除房间
      const res = await deleteRoomInfo(currentRoom.value.id)

      if (res.code === 200) {
        // 从树节点中移除该房间
        if (currentFloorNode.value && currentFloorNode.value.children) {
          const roomIndex = currentFloorNode.value.children.findIndex(item => item.id === currentRoom.value.id)
          if (roomIndex !== -1) {
            currentFloorNode.value.children.splice(roomIndex, 1)
          }
        }

        // 清空当前房间
        currentRoom.value = null

        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除房间失败:', error)
      ElMessage.error('删除房间失败')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}

// 表单引用
const bedFormRef = ref(null)
const editBedFormRef = ref(null)

// 床位表单验证规则
const bedRules = {
  bedNo: [
    { required: true, message: '请输入床位号', trigger: 'blur' },
    { pattern: /^[0-9A-Za-z-]+$/, message: '床位号只能包含数字、字母和横杠', trigger: 'blur' }
  ],
  bedCode: [
    { required: true, message: '请输入床位编码', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入床位价格', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的价格数字', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择床位类型', trigger: 'change' }]
}

// 添加床位
const showAddBedDialog = ref(false)
const addBedForm = ref({
  bedNo: '',
  bedCode: '',
  price: 0,
  type: '',
  remark: ''
})

function handleAddBed() {
  showAddBedDialog.value = true
  addBedForm.value = {
    bedNo: '',
    bedCode: '',
    price: 0,
    type: '',
    remark: ''
  }
  // 重置表单验证
  nextTick(() => {
    bedFormRef.value?.resetFields()
  })
}

async function submitAddBed() {
  bedFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 准备提交到API的数据
        const bedData = {
          roomId: currentRoom.value.id,
          bedNumber: addBedForm.value.bedNo,
          bedCode: addBedForm.value.bedCode,
          bedType: addBedForm.value.type,
          bedPrice: addBedForm.value.price,
          remark: addBedForm.value.remark
        }

        // 调用API添加床位
        const res = await addBedInfo(bedData)

        if (res.code === 200) {
          // 重新获取床位列表
          await fetchRoomBeds(currentRoom.value.id)
          ElMessage.success('添加成功')
          showAddBedDialog.value = false
          // 重置表单
          addBedForm.value = {
            bedNo: '',
            bedCode: '',
            price: 0,
            type: '',
            remark: ''
          }
          bedFormRef.value.resetFields()
        } else {
          ElMessage.error(res.msg || '添加失败')
        }
      } catch (error) {
        console.error('添加床位失败:', error)
        ElMessage.error('添加床位失败')
      }
    }
  })
}


// 编辑床位弹框
const showEditBedDialog = ref(false)
const editBedForm = ref({
  bedNo: '',
  bedCode: '',
  price: 0,
  type: '',
  remark: ''
})
const currentEditBed = ref(null)

// 编辑床位
function handleEditBed(bed) {
  currentEditBed.value = bed
  Object.assign(editBedForm.value, {
    bedNo: bed.bedNo,
    bedCode: bed.bedCode,
    price: bed.price || 0,
    type: bed.type,
    remark: bed.remark || ''
  })
  showEditBedDialog.value = true
}

// 提交编辑床位
async function handleEditBedSubmit() {
  editBedFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 准备提交到API的数据
        const bedData = {
          id: currentEditBed.value.id,
          roomId: currentRoom.value.id,
          bedNumber: editBedForm.value.bedNo,
          bedCode: editBedForm.value.bedCode,
          bedType: editBedForm.value.type,
          bedPrice: editBedForm.value.price,
          remark: editBedForm.value.remark
        }

        // 调用API更新床位
        const res = await updateBedInfo(bedData)

        if (res.code === 200) {
          // 重新获取床位列表
          await fetchRoomBeds(currentRoom.value.id)
          ElMessage.success('修改成功')
          showEditBedDialog.value = false
          // 重置表单
          editBedForm.value = {
            bedNo: '',
            bedCode: '',
            price: 0,
            type: '',
            remark: ''
          }
          editBedFormRef.value.resetFields()
        } else {
          ElMessage.error(res.msg || '修改失败')
        }
      } catch (error) {
        console.error('修改床位失败:', error)
        ElMessage.error('修改床位失败')
      }
    }
  })
}


// 删除床位
function handleDeleteBed(bed) {
  // 检查床位是否已入住
  if (bed.elderId) {
    ElMessage.error('该床位已有老人入住，不允许删除')
    return
  }

  ElMessageBox.confirm('确认删除该床位吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用API删除床位
      const res = await deleteBedInfo(bed.id)

      if (res.code === 200) {
        // 重新获取床位列表
        await fetchRoomBeds(currentRoom.value.id)
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除床位失败:', error)
      ElMessage.error('删除床位失败')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}
</script>

<style scoped lang="scss">
.buildmanage-page {
  background: #fff;
  min-height: 100vh;
  padding: 24px;
}

.page-container {
  display: flex;
  gap: 24px;
  min-height: calc(100vh - 48px);
}

.left-panel {
  width: 300px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}

.right-panel {
  flex: 1;
  border: 1px solid #e5e6eb;
  border-radius: 4px;

  &.empty-panel {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;

  .node-content {
    display: flex;
    align-items: center;
  }
}

.operation-btns {
  opacity: 0;
  transition: opacity 0.2s;
}

.custom-tree-node:hover .operation-btns {
  opacity: 1;
}

.room-info {
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style>