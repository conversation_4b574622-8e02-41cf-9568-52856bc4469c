# 护士排班系统 (schedulingHf)

## 功能概述

这是一个完整的护士排班管理系统，支持电脑端操作，包含筛选条件、数据表格、操作功能等完整模块。

## 主要功能

### 1. 筛选条件
- **排班日期起止**：日期选择，默认显示当前排班周期，支持上一周期、下一周期切换
- **区域**：下拉框，可多选，默认全部
- **班次**：下拉或多选框，默认空，代表全部班次，可多选（早/中/晚/夜班/休班/请假/调休）
- **人员**：文本框，默认空，支持姓名/工号模糊查询
- **状态**：下拉或多选，选项：未排班/已排班

### 2. 数据表格
- 表头显示当前周期所属的年、月
- 横向第一行为当前周期第一天至最后一天的日期、星期几，数据行按行变色
- 纵向第一列为全部待排班人员
- 交叉点为人员排班数据（班次/区域）

### 3. 操作功能

#### 增加人员
- 从"待排班人员表"选择待排班人员
- 排班表中已有的不可选或选后无操作
- 排班表中没有的追加至排班表中，可全选、多选

#### 单元格设置
1. 单元格点击→弹窗选择班次、区域
2. 设置完成班次、区域后，点击确定后，更新当前单元格

#### 批量设置
1. Ctrl+点击选择不连续单元格→右键弹窗选择班次/区域
2. 按住左键拖拉选择连续单元格→右键弹窗选择班次/区域
3. 设置完成班次、区域后，点击确定后，更新所有选中的当前单元格

#### 导入功能
- 表头按钮，提供数据模版
- 支持使用数据文件导入已排好的排班表

#### 工时统计
- 增加统计日期起止（日期选择框）
- 图表展示工时统计结果

#### 冲突提示
- 红色波浪线标注冲突单元格
- 根据数据校验规则识别

#### 自动保存
- 单元格设置完成后自动保存

### 4. 数据校验规则
- 同时间段同一区域不可重复排班
- 护士每日仅允许一个班次（特殊岗位除外）
- 休班日与值班日互斥校验
- 支持设置规则是否生效

### 5. 辅助模块

#### 待排班人员选择
- 从用户表中选择待排班人员
- 支持多选，默认按选择顺序排序
- 允许修改排序
- 支持设定身份(护士、护理员、后期、实习生等)

## 技术特性

### 前端技术栈
- Vue 3 + Composition API
- Element Plus UI 组件库
- ECharts 图表库
- SCSS 样式预处理器

### 交互特性
- 响应式设计，支持不同屏幕尺寸
- 键盘快捷键支持（Ctrl+C复制、Ctrl+V粘贴、Delete删除、ESC取消）
- 右键菜单操作
- 拖拽选择和Ctrl多选
- 实时数据更新

### 数据管理
- 模拟数据展示
- 本地状态管理
- 表单验证
- 冲突检测

## 使用说明

### 基本操作
1. 选择排班日期范围
2. 设置筛选条件（可选）
3. 点击查询按钮获取数据
4. 在表格中点击单元格进行排班设置
5. 使用Ctrl+点击进行多选操作
6. 右键菜单进行批量操作

### 快捷键
- `Ctrl + 点击`：多选单元格
- `右键`：显示上下文菜单
- `Ctrl + C`：复制选中单元格的排班
- `Ctrl + V`：粘贴排班到选中单元格
- `Delete`：删除选中单元格的排班
- `ESC`：取消选择

### 班次类型
- **早班**：绿色标识
- **中班**：橙色标识
- **晚班**：红色标识
- **夜班**：灰色标识
- **休班**：蓝色标识
- **请假**：浅灰色标识
- **调休**：浅绿色标识

## 文件结构

```
schedulingHf/
├── index.vue          # 主组件文件
└── README.md         # 说明文档
```

## 扩展功能

系统预留了以下扩展接口：
- API 接口集成
- 数据库连接
- 权限控制
- 审批流程
- 消息通知
- 数据导出

## 注意事项

1. 当前使用模拟数据，实际使用时需要连接后端API
2. 冲突检测规则可根据实际业务需求调整
3. 班次和区域配置可通过后台管理进行维护
4. 建议在生产环境中添加数据备份和恢复功能
